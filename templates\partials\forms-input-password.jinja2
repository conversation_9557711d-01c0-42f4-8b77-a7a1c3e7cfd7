<div data-signals="{_{{ namealwayschange }}isPasswordBOOL: true}" >
  <style>
      me {
        position: relative;
        height: 95px;
        margin-top: 16px;
        margin-bottom: 16px;
        flex-basis: 100%;
      }

      me input {
        height: 32px;
        position: absolute;
        top: 30px;
        left: 0;
        right: 0;
        font-family: 'Noto Sans', sans-serif;
        font-size: 18px;
        color: var(--color-text-black);
        border: 0;
        z-index: 1;
        background-color: transparent;
        border-bottom: 1px solid var(--color-input-lines);
        padding: 0;
        border-radius: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;

        &:focus {
            outline: 0;
            border-bottom: 1px solid var(--color-input-lines);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                top: 10px;
            }
        }

        &:valid {
            border-bottom: 1px solid var(--color-selected-green);

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                top: 10px;
            }
        }

        &:not(:placeholder-shown):invalid {
            border-bottom: 1px solid var(--color-selected-red);
            animation: error-shake 600ms;

            &+.input-label {
                font-family: 'Noto Serif', serif;
                font-style: italic;
                font-size: 15px;
                color: var(--color-text-dark);
                top: 10px;
            }
        }

        &:not(:placeholder-shown):not(:focus):invalid~.input-group__error {
            visibility: visible;
            opacity: 1;
        }

        /* Hide error on page load for fields with default values */
        &[data-touched="false"]:not(:focus)~.input-group__error {
            visibility: hidden;
            opacity: 0;
        }

        &:disabled {
            color: var(--color-disabled);
            border-bottom: 1px solid var(--color-disabled);
        }
        &:disabled + .input-label {
            color: var(--color-disabled);
        }
        &:disabled + .input-label.float-label {
            color: var(--color-disabled);
        }
      }

      me .input-group__error {
          position: absolute;
          top: 70px;
          left: 10px;
          color: var(--color-selected-red);
          display: block;
          visibility: hidden;
          opacity: 0;
          font-family: 'Noto Serif', serif;
          font-size: 14px;
          transition: all 0.3s ease-out;
      }
  </style>

  <input data-attr-type="$_{{ namealwayschange }}isPasswordBOOL ? 'password' : 'text'"
         pattern="((?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[\W]).{8,64})"
         placeholder=" "
         {% if value %}value="{{ value }}" data-touched="false"{% else %}data-touched="true"{% endif %}
         name="{{ name | default(namealwayschange) }}"
         required
         onblur="this.setAttribute('data-touched', 'true')"
         oninput="this.setAttribute('data-touched', 'true')" />

  <label class="input-label" data-attr-disabled="$_{{ namealwayschange }}isPasswordBOOL">
    <style>
        me {
          position: absolute;
          top: 36px;
          left: 0;
          color: var(--color-text-black);
          transition: .15s ease;
        }
        .input-label:disabled, input:disabled + .input-label {
            color: var(--color-disabled);
        }
        input:disabled + .input-label.float-label {
            color: var(--color-disabled);
        }
    </style>
    {{ label }}
  </label>

  <span class="input-group__error">Password invalid
    <button type="button" popovertarget="password-info-popover">
      <style>
          me {
              all: unset;
              background-color: transparent;
              color: var(--color-error-title);
              width: auto;
              height: auto;
              margin-left: 8px;
              text-align: center;
              cursor: pointer;
              transition: .25s ease;
          }

          me:hover {
              text-decoration: underline;
          }
      </style>
      More Info...
    </button>
  </span>

  <span class="fa-regular fa-eye" data-on-click="$_{{ namealwayschange }}isPasswordBOOL=!$_{{ namealwayschange }}isPasswordBOOL" data-class-fa-eye="$_{{ namealwayschange }}isPasswordBOOL" data-class-fa-eye-slash="!$_{{ namealwayschange }}isPasswordBOOL">
    <style>
        me {
            color: var(--color-input-lines);
            font-size: 18px;
            position: absolute;
            right: 6px;
            top: 40px;
            z-index: 2;
        }
    </style>
  </span>

</div>
