<div class="info-popover-container">
  <style>
      me {
          position: relative;
          display: inline-block;
      }

      me .info-button {
          all: unset;
          margin-bottom: {{ marginbottom }}px;
          font-family: 'Noto Serif', serif;
          font-size: 18px;
          font-weight: 500;
          background-color: transparent;
          border: 1px solid var(--color-input-lines);
          border-radius: 8px;
          color: var(--color-input-lines);
          width: 25px;
          height: 25px;
          margin-left: 8px;
          text-align: center;
          cursor: pointer;
          transition: background-color 0.3s ease, color 0.3s ease;
          display: inline-block;
      }

      me .info-button:hover {
          background-color: var(--color-background-dark);
          color: var(--color-text-brighter);
      }

      me .info-content {
          position: fixed;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background-color: var(--color-text-dark);
          border: 1px solid var(--color-background-dark);
          border-radius: 14px;
          padding: 4px;
          z-index: 1000;
          opacity: 0;
          visibility: hidden;
          transition: opacity 0.3s ease, visibility 0.3s ease;
      }

      me .info-content.show {
          opacity: 1;
          visibility: visible;
      }
  </style>

  <button type="button" class="info-button" id="info_btn_{{ namealwayschange }}" tabindex="0">i</button>
  <div class="info-content" id="info_content_{{ namealwayschange }}">
    {{ infohtml | safe }}
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const button = document.getElementById('info_btn_{{ namealwayschange }}');
  const content = document.getElementById('info_content_{{ namealwayschange }}');

  if (button && content) {
    // Toggle popover on button click
    button.addEventListener('click', function(e) {
      e.stopPropagation();
      content.classList.toggle('show');
    });

    // Close popover when clicking outside
    document.addEventListener('click', function(e) {
      if (!button.contains(e.target) && !content.contains(e.target)) {
        content.classList.remove('show');
      }
    });

    // Close popover on Escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && content.classList.contains('show')) {
        content.classList.remove('show');
        button.focus(); // Return focus to button
      }
    });
  }
});
</script>
